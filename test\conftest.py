"""
SPACE2测试配置文件 - pytest配置
遵循CLAUDE.md规范，为SPACE2系统提供统一的测试配置
"""

import pytest
import sys
import os
from pathlib import Path

# 添加src路径到Python路径
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

# 测试配置
def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "orbital: mark test as orbital module test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )

@pytest.fixture(scope="session")
def test_data_dir():
    """测试数据目录夹具"""
    return project_root / "src" / "env" / "env_data"

@pytest.fixture(scope="session") 
def test_config_dir():
    """测试配置目录夹具"""
    return project_root / "src" / "env" / "physics_layer"

@pytest.fixture(scope="session")
def test_output_dir():
    """测试输出目录夹具"""
    output_dir = project_root / "test" / "output"
    output_dir.mkdir(exist_ok=True)
    return output_dir