# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is the SPACE2 satellite edge computing simulation environment - a LEO satellite constellation edge-cloud collaborative computing simulation platform based on PettingZoo framework supporting multi-agent reinforcement learning algorithms.

The system simulates 72 LEO satellites, 420 ground user terminals, and 5 cloud computing centers in complex interactions across 1441 timeslots (7205 seconds total simulation time).

## Project Structure
- `src/env/` - All environment-related source code
  - `Foundation_Layer/` - Core infrastructure (error handling, logging, time management)
  - `physics_layer/` - Physical simulation components (orbital dynamics, config)
  - `env_data/` - Simulation data files (satellite data, ground stations, cloud stations)
- `src/agent/` - Agent-related code (currently empty)
- `test/` - Test files matching source code structure
- `techincal_files/` - Technical documentation and supplementary files
- `env.md` - Comprehensive technical documentation (in Chinese)

## Configuration
Main configuration file: `src/env/physics_layer/config.yaml`
- System parameters (satellites: 72 LEO, users: 420, timeslots: 1441)
- Communication model parameters
- Computation model parameters
- Observation space normalization
- Hybrid simulation parameters

## Development Guidelines

### Code Organization Principles
- **Separation of Concerns**: Environment source code in `src/env/`, tests in `/test/`
- **Simplicity**: Source code focuses on core logic without test/output code
- **Decoupling**: Loose coupling between modules
- **Clear Interfaces**: Expose appropriate interfaces for integration

### Testing Approach
- Test by outputting calculation results per timeslot
- Test file structure mirrors source code structure for easy maintenance
- Tests validate computational accuracy through timeslot-based result verification

### Development Workflow
1. Requirements Analysis → Programming Planning → Test Method Design
2. Programming Implementation → Test Verification → Merge to Main Branch
3. Documentation + Test Documentation

### Coding Standards
- **Language**: Python 3.9+
- **Style**: PEP8 compliant with clear comments (classes, methods, key logic blocks)
- **Naming**: Consistent use of snake_case
- **Modules**: Each module should provide `__init__.py`

### Performance Optimization
- **Matrix Operations**: Prefer NumPy matrix/vectorized operations over for loops to reduce complexity
  - Example: Use `np.dot(A, B)` instead of nested loops
- **Sparse Representation**: Use `scipy.sparse` for large network/connection matrices to save memory
- **Batch Processing**: Avoid processing data individually, prioritize batch design
- **Caching**: Cache orbital data, visibility windows, etc., or pre-compute them

### Decoupling and Modularization
- Physical layer, communication layer, and agents should be independent, communicating through well-defined interfaces
- Data loading should be implemented through a unified DataLoader module
- Hard-coded paths or experimental parameters are not allowed in core modules

### Logging and Error Handling
- All modules should support logger with debug/info/error multi-level output
- Exceptions must be handled structurally, no `try: ... except: pass` allowed

## Key Components
Based on the codebase structure and documentation:

1. **Time Management**: Core time synchronization and context management
2. **Orbital Dynamics**: LEO satellite position and visibility calculations using ECEF coordinate system
   - Data source: `satellite_data72_1.csv` with 103,752 records (72 satellites × 1441 timeslots)
   - Supports three visibility matrices: satellite-satellite, satellite-ground, satellite-cloud
   - Vectorized distance calculations for performance optimization
3. **Error Handling**: Structured error management framework
4. **Logging**: Comprehensive logging configuration
5. **Data Processing**: Satellite, ground station, and cloud center data handling

## Architecture Notes
- This is a complex multi-agent simulation environment
- Uses layered architecture: Foundation → Physics → Environment layers
- Supports both traditional and hybrid simulation modes
- Integrates with PettingZoo for reinforcement learning compatibility
- **Scalable Satellite Count**: Current configuration uses 72 LEO satellites, but the system is designed to support variable satellite counts for testing different constellation scales

## Recent Implementation Updates

### Orbital Module (orbital.py) - Version 2.0
- **Data Source**: Updated to use `satellite_data72_1.csv` (103,752 satellite records)
- **Format**: `satillite_ID,time_slot,time,lat,lon,light,state`
- **Visibility Matrices**: Implements three types of visibility calculations:
  - Inter-satellite visibility (72×72 matrix)
  - Satellite-to-ground visibility (72×420 matrix) 
  - Satellite-to-cloud visibility (72×5 matrix)
- **Distance Thresholds**: 
  - Satellite-satellite: 2000km
  - Satellite-ground: 1000km
  - Satellite-cloud: 1200km
- **Coordinate System**: ECEF (Earth-Centered, Earth-Fixed) for accurate 3D distance calculations
- **Performance**: Vectorized NumPy operations for matrix computations
- **Testing**: Complete test coverage with per-timeslot result verification

## Development Standards (Extended Guidelines)

### Performance Optimization (Enhanced)
- **Vectorized Operations**: All distance calculations use NumPy vectorization
- **Memory Management**: Large matrices handled efficiently with appropriate data types
- **Caching Strategy**: Orbital positions and visibility results cached per timeslot
- **Testing Approach**: Output complete matrices for verification, not just summaries

### Scalability Requirements
- **Variable Satellite Count**: System supports different constellation sizes (18, 36, 72, etc.)
- **Modular Design**: Orbital calculations independent of other system components
- **Future Testing**: Different scales of satellite constellations planned for performance evaluation