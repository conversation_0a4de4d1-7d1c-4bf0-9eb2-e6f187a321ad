# SPACE2环境模块完成情况跟踪

> 文档版本: v1.0.0  
> 最后更新: 2025-08-09  
> 项目路径: D:\paper\space\SPACE2

## 一、概述

### 1.1 项目背景
SPACE2是一个LEO卫星星座边缘-云协同计算仿真平台，基于PettingZoo框架支持多智能体强化学习算法。系统模拟72颗LEO卫星、420个地面用户终端和5个云计算中心在1441个时隙（7205秒）内的复杂交互。

### 1.2 当前完成的模块
| 模块名称 | 文件路径 | 完成状态 | 版本 |
|---------|---------|---------|------|
| 配置管理 | `src/env/physics_layer/config.yaml` | ✅ 完成 | v1.0 |
| 时间管理 | `src/env/Foundation_Layer/time_manager.py` | ✅ 完成 | v1.0 |
| 轨道动力学 | `src/env/physics_layer/orbital.py` | ✅ 完成 | v2.0 |
| 通信链路管理 | `src/env/physics_layer/communication_refactored.py` | ✅ 完成 | v1.0 |
| 错误处理 | `src/env/Foundation_Layer/error_handler.py` | ✅ 完成 | v1.0 |
| 日志管理 | `src/env/Foundation_Layer/logger_config.py` | ✅ 完成 | v1.0 |

### 1.3 模块依赖关系
```
                    config.yaml
                         |
                         v
                  TimeManager
                         |
                         v
                 OrbitalUpdater
                         |
                         v
              CommunicationManager
                         |
                         v
                 [待开发模块]
              /        |        \
             v         v         v
    ComputationManager TaskScheduler Environment
```

## 二、配置管理 (config.yaml)

### 2.1 文件说明
- **位置**: `src/env/physics_layer/config.yaml`
- **作用**: 集中管理所有系统参数，避免硬编码，支持参数调优
- **格式**: YAML格式，支持注释和层级结构

### 2.2 主要配置项

#### 系统架构参数
```yaml
system:
  num_users: 420                    # 地面用户终端数量
  num_leo_satellites: 72            # LEO卫星数量
  leo_altitude_m: 1200000           # LEO卫星轨道高度 (m)
  earth_radius_m: 6371000           # 地球半径 (m)
  timeslot_duration_s: 4            # 时隙持续时间 (秒)
  total_timeslots: 1500             # 总时隙数
  visibility_threshold_m: 5500000   # 卫星间可见性阈值距离 (m)
  visibility_earth_m: 2500000       # 地面站-卫星可见性阈值距离 (m)
  cloud_visibility_threshold_m: 3300000  # 云中心-卫星可见性阈值距离 (m)
```

#### 通信模型参数
```yaml
communication:
  rf_carrier_freq_hz: 14000000000   # 射频载波频率 (14 GHz)
  b_us_hz: 100000000                # 用户-卫星上行带宽 (100 MHz)
  p_u_w: 5                          # 用户终端发射功率 (5W)
  p_su_w: 10                        # 卫星对用户发射功率 (10W)
  p_c_w: 50                         # 云中心发射功率 (50W)
  isl_tra_rate: 50000000000         # 星间链路传输速率 (50 Gbps)
  antenna_gain_db: 33.0             # 天线增益 (dB)
```

### 2.3 使用方法
```python
import yaml

# 加载配置
with open('config.yaml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

# 访问参数
num_satellites = config['system']['num_leo_satellites']
carrier_freq = config['communication']['rf_carrier_freq_hz']
```

## 三、时间管理模块 (time_manager.py)

### 3.1 模块作用
统一管理仿真时间和物理时间的映射关系，提供时间上下文服务，确保所有模块时间同步。

### 3.2 核心类和方法

#### TimeContext类
```python
@dataclass
class TimeContext:
    simulation_step: int      # 仿真步数 (0-1440)
    physical_time: datetime   # 物理时间
    timeslot_seconds: float   # 时隙持续时间
    
    @property
    def total_seconds(self) -> float:
        """获取从仿真开始的总秒数"""
```

#### TimeManager类
```python
class TimeManager:
    def __init__(self, start_time: datetime, timeslot_duration: float, total_timeslots: int)
    def get_time_context(self, simulation_step: int) -> TimeContext
    def is_valid_step(self, step: int) -> bool
    def get_elapsed_time(self, from_step: int, to_step: int) -> float
```

### 3.3 接口调用示例
```python
from env.Foundation_Layer.time_manager import create_time_manager_from_config

# 从配置创建时间管理器
time_manager = create_time_manager_from_config(config)

# 获取特定时隙的时间上下文
context = time_manager.get_time_context(100)
print(f"时隙100对应时间: {context.physical_time}")
print(f"已经过秒数: {context.total_seconds}")
```

## 四、轨道动力学模块 (orbital.py)

### 4.1 模块作用
- 管理卫星轨道数据和位置计算
- 计算卫星间、卫星-地面、卫星-云的可见性矩阵
- 提供ECEF坐标系下的3D距离计算

### 4.2 数据源和格式
- **数据文件**: `src/env/env_data/satellite_data72_1.csv`
- **记录数**: 103,752条 (72卫星 × 1441时隙)
- **字段格式**: `satellite_ID, time_slot, time, lat, lon, light, state`

### 4.3 核心功能

#### 4.3.1 卫星位置获取
```python
def get_satellites_at_time(self, time_step: int) -> Dict[str, Satellite]
```
返回指定时隙所有卫星的位置和状态。

#### 4.3.2 可见性矩阵计算
```python
# 卫星间可见性 (72×72矩阵)
def build_visibility_matrix(satellites) -> Tuple[np.ndarray, np.ndarray]

# 卫星-地面可见性 (72×420矩阵)  
def build_satellite_ground_visibility_matrix(satellites, time_step) -> Tuple[np.ndarray, np.ndarray]

# 卫星-云可见性 (72×5矩阵)
def build_satellite_cloud_visibility_matrix(satellites, time_step) -> Tuple[np.ndarray, np.ndarray]
```

### 4.4 主要接口

#### 初始化
```python
from env.physics_layer.orbital import OrbitalUpdater

orbital = OrbitalUpdater(
    data_file='satellite_data72_1.csv',  # 可选，有默认值
    config_file='config.yaml',           # 可选，有默认值
    time_manager=time_manager            # 可选，自动创建
)
```

#### 获取卫星数据
```python
# 获取时隙100的所有卫星
satellites = orbital.get_satellites_at_time(100)

# 获取卫星ID列表
sat_ids = orbital.get_satellite_ids(100)

# 获取可见性矩阵和距离矩阵
visibility, distances = orbital.build_visibility_matrix(satellites)
```

### 4.5 使用示例
```python
# 完整示例：计算时隙100的卫星间可见链路
orbital = OrbitalUpdater()
satellites = orbital.get_satellites_at_time(100)
visibility, distances = orbital.build_visibility_matrix(satellites)

# 统计可见链路
num_visible_links = np.sum(visibility)
avg_distance = np.mean(distances[visibility])
print(f"时隙100: {num_visible_links}条可见链路，平均距离{avg_distance:.1f}km")
```

## 五、通信链路管理模块 (communication_refactored.py)

### 5.1 模块作用
基于轨道模块提供的距离数据，使用物理模型计算所有通信链路的质量指标，包括路径损耗、信噪比和数据速率。

### 5.2 物理模型

#### 自由空间路径损耗 (FSPL)
```
FSPL(dB) = 20·log10(d) + 20·log10(f) + 20·log10(4π/c)
```

#### 接收功率
```
Pr(dBm) = Pt(dBm) + Gt(dB) + Gr(dB) - FSPL(dB) - L_other(dB)
```

#### Shannon容量
```
C = B · log2(1 + SNR)
```

### 5.3 链路类型

| 链路类型 | 技术 | 数据速率 | 功率配置 | 带宽 |
|---------|------|---------|---------|------|
| ISL星间链路 | 激光 | 50 Gbps | - | - |
| 用户→卫星 | RF | ~45-50 Mbps | 5W | 100 MHz |
| 卫星→用户 | RF | ~125-130 Mbps | 10W | 120 MHz |
| 云→卫星 | RF | ~320 Mbps | 50W | 180 MHz |
| 卫星→云 | RF | ~135 Mbps | 15W | 150 MHz |

### 5.4 主要接口

#### 初始化
```python
from env.physics_layer.communication_refactored import CommunicationManager

comm = CommunicationManager(
    orbital_updater=orbital,  # 可选，自动创建
    config_file='config.yaml' # 可选，有默认值
)
```

#### 获取通信矩阵
```python
# ISL星间链路
isl_comm = comm.get_isl_communication_matrix(time_step)

# 卫星-地面链路
sat_ground_comm = comm.get_satellite_ground_communication_matrix(time_step)

# 卫星-云链路
sat_cloud_comm = comm.get_satellite_cloud_communication_matrix(time_step)

# 获取汇总指标
metrics = comm.get_link_quality_metrics(time_step)
```

### 5.5 使用示例
```python
# 完整示例：分析时隙100的通信链路
comm = CommunicationManager()

# 获取ISL链路数据
isl = comm.get_isl_communication_matrix(100)
print(f"ISL链路数: {np.sum(isl['visibility'])}")
print(f"平均数据率: {np.mean(isl['data_rate_bps'])/1e9:.1f} Gbps")

# 获取卫星-地面链路
ground = comm.get_satellite_ground_communication_matrix(100)
visible = ground['visibility']
print(f"地面可见链路: {np.sum(visible)}")
print(f"平均上行速率: {np.mean(ground['uplink_data_rate_bps'][visible])/1e6:.1f} Mbps")
```

### 5.6 输出数据格式

#### ISL通信矩阵返回格式
```python
{
    'data_rate_bps': np.ndarray,      # 数据速率矩阵 (bps)
    'snr_db': np.ndarray,              # 信噪比矩阵 (dB)
    'distance_km': np.ndarray,         # 距离矩阵 (km)
    'visibility': np.ndarray,          # 可见性布尔矩阵
    'propagation_delay_ms': np.ndarray,# 传播延迟 (ms)
    'link_type': 'laser',              # 链路类型
    'satellite_ids': list              # 卫星ID列表
}
```

#### 地面/云链路返回格式
```python
{
    'uplink_data_rate_bps': np.ndarray,   # 上行速率
    'uplink_snr_db': np.ndarray,          # 上行SNR
    'downlink_data_rate_bps': np.ndarray, # 下行速率
    'downlink_snr_db': np.ndarray,        # 下行SNR
    'distance_km': np.ndarray,            # 距离矩阵
    'visibility': np.ndarray,             # 可见性矩阵
    'propagation_delay_ms': np.ndarray,   # 传播延迟
    'satellite_ids': list,                # 卫星ID列表
    'ground_station_ids': list            # 地面站ID列表
}
```

## 六、模块集成指南

### 6.1 初始化顺序
```python
# 1. 加载配置
config = load_config('config.yaml')

# 2. 创建时间管理器
time_manager = create_time_manager_from_config(config)

# 3. 创建轨道更新器
orbital = OrbitalUpdater(config_file='config.yaml', time_manager=time_manager)

# 4. 创建通信管理器
comm = CommunicationManager(orbital_updater=orbital, config_file='config.yaml')

# 5. [待开发] 创建计算管理器
# compute = ComputationManager(config_file='config.yaml')

# 6. [待开发] 创建任务调度器
# scheduler = TaskScheduler(orbital, comm, compute)

# 7. [待开发] 创建环境
# env = SPACE2Environment(scheduler)
```

### 6.2 数据流向
```
配置文件 → TimeManager → OrbitalUpdater → CommunicationManager
                              ↓
                         位置/可见性数据
                              ↓
                    [ComputationManager] ← 链路质量数据
                              ↓
                        [TaskScheduler]
                              ↓
                         [Environment]
```

### 6.3 典型使用场景

#### 场景1：单时隙分析
```python
def analyze_single_timestep(timestep):
    orbital = OrbitalUpdater()
    comm = CommunicationManager(orbital)
    
    # 获取该时隙的所有信息
    satellites = orbital.get_satellites_at_time(timestep)
    isl = comm.get_isl_communication_matrix(timestep)
    metrics = comm.get_link_quality_metrics(timestep)
    
    return {
        'num_satellites': len(satellites),
        'isl_links': np.sum(isl['visibility']),
        'avg_isl_rate_gbps': metrics['isl']['avg_data_rate_gbps']
    }
```

#### 场景2：时序分析
```python
def analyze_time_series(start, end):
    orbital = OrbitalUpdater()
    comm = CommunicationManager(orbital)
    results = []
    
    for t in range(start, end):
        metrics = comm.get_link_quality_metrics(t)
        results.append({
            'timestep': t,
            'total_links': sum(m['total_links'] for m in metrics.values())
        })
    
    return pd.DataFrame(results)
```

## 七、测试验证

### 7.1 已完成的测试

| 测试模块 | 测试文件 | 覆盖范围 |
|---------|---------|---------|
| 轨道动力学 | `test/physics_layer/test_orbital.py` | 位置计算、可见性矩阵、距离计算 |
| 通信链路 | `test/physics_layer/test_communication_detailed.py` | 路径损耗、SNR、数据速率、全时隙测试 |

### 7.2 测试数据说明

#### 生成的测试文件
- `communication_summary_stats.csv`: 17个时隙的汇总统计
- `communication_isl_links.csv`: 3654条ISL链路详细数据
- `communication_sat_ground_links.csv`: 13706条地面链路数据
- `communication_sat_cloud_links.csv`: 302条云链路数据

### 7.3 性能指标

| 操作 | 执行时间 | 备注 |
|-----|---------|------|
| 加载卫星数据 | ~60ms | 103,752条记录 |
| 计算可见性矩阵 | ~2ms | 72×72矩阵，使用缓存<1ms |
| 获取通信矩阵 | ~3ms | 包含所有计算 |
| 全时隙测试(17个) | ~2s | 包含文件I/O |

## 八、待开发模块

### 8.1 计算资源管理 (computation.py)
- [ ] 卫星计算资源建模
- [ ] 任务执行时间计算
- [ ] 能耗模型
- [ ] 资源分配策略

### 8.2 任务调度 (task_scheduler.py)
- [ ] 任务队列管理
- [ ] 优先级调度
- [ ] 多跳卸载决策
- [ ] 负载均衡

### 8.3 环境封装 (environment.py)
- [ ] PettingZoo接口实现
- [ ] 观测空间定义
- [ ] 动作空间定义
- [ ] 奖励函数

### 8.4 数据生成器 (data_generator.py)
- [ ] 任务生成
- [ ] 用户移动模型
- [ ] 流量模式

## 附录

### A. API快速参考

```python
# 时间管理
time_manager = create_time_manager_from_config(config)
context = time_manager.get_time_context(step)

# 轨道动力学
orbital = OrbitalUpdater()
satellites = orbital.get_satellites_at_time(step)
vis, dist = orbital.build_visibility_matrix(satellites)

# 通信管理
comm = CommunicationManager()
isl = comm.get_isl_communication_matrix(step)
ground = comm.get_satellite_ground_communication_matrix(step)
cloud = comm.get_satellite_cloud_communication_matrix(step)
```

### B. 常见问题

**Q1: 为什么部分链路数据速率为0？**
- A: 当SNR < 0 dB时，链路质量太差无法通信，速率设为0。这通常发生在接近可见性边界的远距离链路。

**Q2: 如何调整可见性阈值？**
- A: 修改`config.yaml`中的`visibility_threshold_m`、`visibility_earth_m`、`cloud_visibility_threshold_m`参数。

**Q3: 缓存如何工作？**
- A: 轨道和通信模块都实现了缓存机制，相同时隙的重复查询会直接返回缓存结果。使用`clear_cache()`方法清除缓存。

### C. 性能优化建议

1. **使用向量化操作**: 所有矩阵计算都使用NumPy向量化，避免Python循环
2. **启用缓存**: 对于重复查询的时隙，缓存可提供>1000倍加速
3. **批量处理**: 尽量批量获取多个时隙的数据，减少函数调用开销
4. **预计算**: 可预先计算并存储所有时隙的可见性矩阵

---

## 更新日志

### v1.0.0 (2025-08-09)
- 初始版本
- 完成基础模块：配置管理、时间管理、轨道动力学、通信链路管理
- 添加完整的测试套件和文档

### 待更新内容预留
- [ ] v1.1.0: 添加计算资源管理模块
- [ ] v1.2.0: 添加任务调度模块
- [ ] v1.3.0: 添加环境封装模块
- [ ] v2.0.0: 完整的多智能体环境实现

---

*本文档将随项目进展持续更新，每个新模块完成后会在相应章节添加详细说明。*