# 程序完成情况检查提示词

## 检查指令
请基于提供的说明文档，对程序进行全面的完成情况检查。请从以下维度进行详细分析：

### 1. 功能完成度检查
- **需求对照**：逐一对比说明文档中的功能需求，确认每个功能点是否已实现
- **功能完整性**：检查是否存在遗漏的功能模块或特性
- **边界条件处理**：验证异常情况和边界条件的处理是否完善
- **用户体验**：评估功能实现是否符合用户预期和使用习惯

### 2. 性能评估
- **执行效率**：分析算法复杂度，识别性能瓶颈
- **资源使用**：评估内存、CPU、存储等资源的使用情况
- **响应时间**：检查关键操作的响应速度是否满足要求
- **并发处理**：评估多用户或高并发场景下的性能表现
- **可扩展性**：分析系统在负载增长时的扩展能力

### 3. 技术方法评估
- **技术选型**：评估所使用的技术栈是否合适
- **架构设计**：检查系统架构的合理性和可维护性
- **算法实现**：分析核心算法的正确性和效率
- **数据结构**：评估数据结构选择的合适性
- **设计模式**：检查是否正确应用了相关设计模式

### 4. 代码质量检查
- **代码规范**：检查代码风格、命名规范、注释完整性
- **可读性**：评估代码的清晰度和可理解性
- **可维护性**：分析代码的模块化程度和修改难度
- **错误处理**：检查异常处理机制是否完善
- **安全性**：识别潜在的安全漏洞和风险点

### 5. 系统集成检查
- **前置依赖**：检查与前序程序模块的接口对接情况
- **数据流转**：验证数据在各模块间的传递是否正确
- **后续衔接**：确认输出格式和接口是否满足后续模块需求
- **整体协调**：评估在整个系统中的协调配合情况
- **版本兼容**：检查与其他组件的版本兼容性

### 6. 测试覆盖度
- **单元测试**：检查核心功能的单元测试覆盖情况
- **集成测试**：验证模块间集成的测试完整性
- **系统测试**：评估端到端的系统测试情况
- **性能测试**：检查是否进行了充分的性能测试

## 输出格式要求

### 检查结果概览
- **完成度评分**：X/10 分
- **总体状态**：✅ 已完成 / ⚠️ 部分完成 / ❌ 未完成

### 详细分析报告
请按以下结构输出：

#### ✅ 已完成项目
- [具体功能/特性名称]：完成情况描述

#### ⚠️ 部分完成项目  
- [具体功能/特性名称]：当前状态 + 缺失内容

#### ❌ 未完成项目
- [具体功能/特性名称]：缺失原因分析

#### 🚀 性能评估
- **优势**：列出性能表现良好的方面
- **问题**：指出性能瓶颈和改进点
- **建议**：提供具体的优化建议

#### 🔧 技术方法评价
- **合适的技术选择**：说明理由
- **需要改进的技术实现**：具体建议

#### 🔗 系统集成状况
- **上游对接**：与前置程序的配合情况
- **下游准备**：为后续程序提供的接口状况
- **数据一致性**：数据流转的准确性

#### 📋 改进建议
按优先级排序：
1. **高优先级**：影响核心功能的问题
2. **中优先级**：影响性能和用户体验的问题  
3. **低优先级**：代码质量和维护性改进

#### 🎯 下一步行动计划
- **立即处理**：必须修复的关键问题
- **短期优化**：1-2周内可完成的改进
- **长期规划**：需要较长时间的架构优化

## 注意事项
- 请结合具体的说明文档内容进行检查
- 提供具体的代码示例和改进建议
- 考虑实际的业务场景和用户需求
- 平衡功能完整性与开发效率
- 关注系统的整体一致性和协调性