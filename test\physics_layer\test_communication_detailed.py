"""
通信链路管理模块详细测试
按照SPACE2系统测试要求，输出所有时隙的完整链路计算结果
测试时隙：早期(1-5)、中期(100-105)、后期(1000-1005)
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../src'))

from env.physics_layer.communication_refactored import CommunicationManager
from env.physics_layer.orbital import OrbitalUpdater
from env.Foundation_Layer.time_manager import create_time_manager_from_config


def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_communication_detailed.log'),
            logging.StreamHandler()
        ]
    )


def test_isl_links_detailed(comm_manager, timestep):
    """测试卫星间链路详细数据"""
    isl_comm = comm_manager.get_isl_communication_matrix(timestep)
    
    results = []
    n_satellites = len(isl_comm['satellite_ids'])
    
    # 遍历所有卫星对
    for i in range(n_satellites):
        for j in range(i+1, n_satellites):  # 避免重复
            if isl_comm['visibility'][i, j]:
                results.append({
                    'timestep': timestep,
                    'link_type': 'ISL',
                    'source_id': isl_comm['satellite_ids'][i],
                    'target_id': isl_comm['satellite_ids'][j],
                    'distance_km': isl_comm['distance_km'][i, j],
                    'data_rate_mbps': isl_comm['data_rate_bps'][i, j] / 1e6,
                    'snr_db': isl_comm['snr_db'][i, j],
                    'propagation_delay_ms': isl_comm['propagation_delay_ms'][i, j],
                    'visibility': True
                })
    
    return results


def test_satellite_ground_links_detailed(comm_manager, timestep):
    """测试卫星-地面链路详细数据"""
    sat_ground_comm = comm_manager.get_satellite_ground_communication_matrix(timestep)
    
    results = []
    n_satellites = len(sat_ground_comm['satellite_ids'])
    n_ground_stations = len(sat_ground_comm['ground_station_ids'])
    
    # 遍历所有卫星-地面站对
    for i in range(n_satellites):
        for j in range(n_ground_stations):
            if sat_ground_comm['visibility'][i, j]:
                results.append({
                    'timestep': timestep,
                    'link_type': 'SAT_GROUND',
                    'satellite_id': sat_ground_comm['satellite_ids'][i],
                    'ground_station_id': sat_ground_comm['ground_station_ids'][j],
                    'distance_km': sat_ground_comm['distance_km'][i, j],
                    'uplink_rate_mbps': sat_ground_comm['uplink_data_rate_bps'][i, j] / 1e6,
                    'uplink_snr_db': sat_ground_comm['uplink_snr_db'][i, j],
                    'downlink_rate_mbps': sat_ground_comm['downlink_data_rate_bps'][i, j] / 1e6,
                    'downlink_snr_db': sat_ground_comm['downlink_snr_db'][i, j],
                    'propagation_delay_ms': sat_ground_comm['propagation_delay_ms'][i, j],
                    'visibility': True
                })
    
    return results


def test_satellite_cloud_links_detailed(comm_manager, timestep):
    """测试卫星-云中心链路详细数据"""
    sat_cloud_comm = comm_manager.get_satellite_cloud_communication_matrix(timestep)
    
    results = []
    n_satellites = len(sat_cloud_comm['satellite_ids'])
    n_cloud_stations = len(sat_cloud_comm['cloud_station_ids'])
    
    # 遍历所有卫星-云中心对
    for i in range(n_satellites):
        for j in range(n_cloud_stations):
            if sat_cloud_comm['visibility'][i, j]:
                results.append({
                    'timestep': timestep,
                    'link_type': 'SAT_CLOUD',
                    'satellite_id': sat_cloud_comm['satellite_ids'][i],
                    'cloud_station_id': sat_cloud_comm['cloud_station_ids'][j],
                    'distance_km': sat_cloud_comm['distance_km'][i, j],
                    'uplink_rate_mbps': sat_cloud_comm['uplink_data_rate_bps'][i, j] / 1e6,
                    'uplink_snr_db': sat_cloud_comm['uplink_snr_db'][i, j],
                    'downlink_rate_mbps': sat_cloud_comm['downlink_data_rate_bps'][i, j] / 1e6,
                    'downlink_snr_db': sat_cloud_comm['downlink_snr_db'][i, j],
                    'propagation_delay_ms': sat_cloud_comm['propagation_delay_ms'][i, j],
                    'visibility': True
                })
    
    return results


def test_all_timesteps():
    """测试所有指定时隙的通信链路"""
    print("="*80)
    print("SPACE2通信链路管理模块详细测试")
    print("="*80)
    
    # 初始化通信管理器
    comm_manager = CommunicationManager()
    
    # 定义测试时隙
    test_timesteps = {
        '早期': [1, 2, 3, 4, 5],
        '中期': [100, 101, 102, 103, 104, 105],
        '后期': [1000, 1001, 1002, 1003, 1004, 1005]
    }
    
    # 存储所有结果
    all_isl_results = []
    all_sat_ground_results = []
    all_sat_cloud_results = []
    
    # 统计信息
    summary_stats = []
    
    for phase_name, timesteps in test_timesteps.items():
        print(f"\n{phase_name}时隙测试 ({timesteps[0]}-{timesteps[-1]}):")
        print("-"*60)
        
        for timestep in timesteps:
            print(f"\n处理时隙 {timestep}...")
            
            try:
                # 获取各类链路数据
                isl_results = test_isl_links_detailed(comm_manager, timestep)
                sat_ground_results = test_satellite_ground_links_detailed(comm_manager, timestep)
                sat_cloud_results = test_satellite_cloud_links_detailed(comm_manager, timestep)
                
                # 添加到总结果
                all_isl_results.extend(isl_results)
                all_sat_ground_results.extend(sat_ground_results)
                all_sat_cloud_results.extend(sat_cloud_results)
                
                # 统计当前时隙
                stats = {
                    'timestep': timestep,
                    'phase': phase_name,
                    'isl_links_count': len(isl_results),
                    'sat_ground_links_count': len(sat_ground_results),
                    'sat_cloud_links_count': len(sat_cloud_results),
                    'total_links': len(isl_results) + len(sat_ground_results) + len(sat_cloud_results)
                }
                
                # 计算平均值
                if isl_results:
                    stats['isl_avg_distance_km'] = np.mean([r['distance_km'] for r in isl_results])
                    stats['isl_avg_rate_mbps'] = np.mean([r['data_rate_mbps'] for r in isl_results])
                    stats['isl_avg_delay_ms'] = np.mean([r['propagation_delay_ms'] for r in isl_results])
                    stats['isl_avg_snr_db'] = np.mean([r['snr_db'] for r in isl_results])
                
                if sat_ground_results:
                    stats['sat_ground_avg_distance_km'] = np.mean([r['distance_km'] for r in sat_ground_results])
                    stats['sat_ground_avg_uplink_mbps'] = np.mean([r['uplink_rate_mbps'] for r in sat_ground_results])
                    stats['sat_ground_avg_downlink_mbps'] = np.mean([r['downlink_rate_mbps'] for r in sat_ground_results])
                    stats['sat_ground_avg_delay_ms'] = np.mean([r['propagation_delay_ms'] for r in sat_ground_results])
                
                if sat_cloud_results:
                    stats['sat_cloud_avg_distance_km'] = np.mean([r['distance_km'] for r in sat_cloud_results])
                    stats['sat_cloud_avg_uplink_mbps'] = np.mean([r['uplink_rate_mbps'] for r in sat_cloud_results])
                    stats['sat_cloud_avg_downlink_mbps'] = np.mean([r['downlink_rate_mbps'] for r in sat_cloud_results])
                    stats['sat_cloud_avg_delay_ms'] = np.mean([r['propagation_delay_ms'] for r in sat_cloud_results])
                
                summary_stats.append(stats)
                
                # 打印简要统计
                print(f"  - ISL链路: {stats['isl_links_count']}条")
                print(f"  - 卫星-地面链路: {stats['sat_ground_links_count']}条")
                print(f"  - 卫星-云链路: {stats['sat_cloud_links_count']}条")
                print(f"  - 总链路数: {stats['total_links']}条")
                
            except Exception as e:
                print(f"  错误: {e}")
                logging.error(f"时隙 {timestep} 处理失败: {e}")
    
    return all_isl_results, all_sat_ground_results, all_sat_cloud_results, summary_stats


def save_results_to_csv(isl_results, sat_ground_results, sat_cloud_results, summary_stats):
    """保存结果到CSV文件"""
    print("\n" + "="*80)
    print("保存测试结果")
    print("="*80)
    
    # 保存ISL链路数据
    if isl_results:
        df_isl = pd.DataFrame(isl_results)
        df_isl.to_csv('communication_isl_links.csv', index=False)
        print(f"\nISL链路数据已保存: communication_isl_links.csv")
        print(f"  记录数: {len(df_isl)}")
        print(f"  列: {', '.join(df_isl.columns)}")
    
    # 保存卫星-地面链路数据
    if sat_ground_results:
        df_sat_ground = pd.DataFrame(sat_ground_results)
        df_sat_ground.to_csv('communication_sat_ground_links.csv', index=False)
        print(f"\n卫星-地面链路数据已保存: communication_sat_ground_links.csv")
        print(f"  记录数: {len(df_sat_ground)}")
        print(f"  列: {', '.join(df_sat_ground.columns)}")
    
    # 保存卫星-云链路数据
    if sat_cloud_results:
        df_sat_cloud = pd.DataFrame(sat_cloud_results)
        df_sat_cloud.to_csv('communication_sat_cloud_links.csv', index=False)
        print(f"\n卫星-云链路数据已保存: communication_sat_cloud_links.csv")
        print(f"  记录数: {len(df_sat_cloud)}")
        print(f"  列: {', '.join(df_sat_cloud.columns)}")
    
    # 保存汇总统计
    if summary_stats:
        df_summary = pd.DataFrame(summary_stats)
        df_summary.to_csv('communication_summary_stats.csv', index=False)
        print(f"\n汇总统计已保存: communication_summary_stats.csv")
        print(f"  记录数: {len(df_summary)}")
        
        # 打印汇总表
        print("\n汇总统计表:")
        print("-"*80)
        print(df_summary.to_string(index=False))


def analyze_link_stability():
    """分析链路稳定性"""
    print("\n" + "="*80)
    print("链路稳定性分析")
    print("="*80)
    
    # 读取汇总数据
    try:
        df = pd.read_csv('communication_summary_stats.csv')
        
        # 按阶段分组分析
        phase_analysis = df.groupby('phase').agg({
            'isl_links_count': ['mean', 'std', 'min', 'max'],
            'sat_ground_links_count': ['mean', 'std', 'min', 'max'],
            'sat_cloud_links_count': ['mean', 'std', 'min', 'max'],
            'total_links': ['mean', 'std', 'min', 'max']
        }).round(2)
        
        print("\n各阶段链路数量统计:")
        print(phase_analysis)
        
        # 计算变异系数（稳定性指标）
        print("\n链路稳定性分析（变异系数，越小越稳定）:")
        for phase in ['早期', '中期', '后期']:
            phase_data = df[df['phase'] == phase]
            if not phase_data.empty:
                print(f"\n{phase}阶段:")
                cv_isl = phase_data['isl_links_count'].std() / phase_data['isl_links_count'].mean()
                cv_ground = phase_data['sat_ground_links_count'].std() / phase_data['sat_ground_links_count'].mean()
                cv_cloud = phase_data['sat_cloud_links_count'].std() / phase_data['sat_cloud_links_count'].mean()
                
                print(f"  ISL链路变异系数: {cv_isl:.4f}")
                print(f"  卫星-地面链路变异系数: {cv_ground:.4f}")
                print(f"  卫星-云链路变异系数: {cv_cloud:.4f}")
        
    except Exception as e:
        print(f"分析失败: {e}")


def main():
    """主测试函数"""
    setup_logging()
    
    print("="*80)
    print("SPACE2通信链路管理模块详细测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 运行测试
    isl_results, sat_ground_results, sat_cloud_results, summary_stats = test_all_timesteps()
    
    # 保存结果
    save_results_to_csv(isl_results, sat_ground_results, sat_cloud_results, summary_stats)
    
    # 分析链路稳定性
    analyze_link_stability()
    
    print("\n" + "="*80)
    print("测试完成!")
    print("="*80)
    
    # 输出文件列表
    print("\n生成的文件:")
    print("  1. communication_isl_links.csv - ISL链路详细数据")
    print("  2. communication_sat_ground_links.csv - 卫星-地面链路详细数据")
    print("  3. communication_sat_cloud_links.csv - 卫星-云链路详细数据")
    print("  4. communication_summary_stats.csv - 汇总统计数据")
    print("  5. test_communication_detailed.log - 测试日志")


if __name__ == "__main__":
    main()