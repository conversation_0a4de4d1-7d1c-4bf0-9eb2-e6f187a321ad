"""
统一错误处理框架
提供标准化的异常定义、错误处理策略和恢复机制
解决项目中错误处理不一致的问题
"""
import logging
import traceback
from typing import Optional, Dict, Any, Callable
from enum import Enum
from dataclasses import dataclass
from functools import wraps
import time

logger = logging.getLogger(__name__)


# ====== 异常定义 ======

class SpaceSimulationError(Exception):
    """仿真环境基础异常类"""
    def __init__(self, message: str, error_code: str = None, context: Dict = None):
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.context = context or {}
        super().__init__(message)


class TimeManagementError(SpaceSimulationError):
    """时间管理相关错误"""
    pass


class ResourceError(SpaceSimulationError):
    """资源管理相关错误"""
    pass


class CommunicationError(SpaceSimulationError):
    """通信相关错误"""
    pass


class TaskExecutionError(SpaceSimulationError):
    """任务执行相关错误"""
    pass


class ConfigurationError(SpaceSimulationError):
    """配置相关错误"""
    pass


class DataIntegrityError(SpaceSimulationError):
    """数据完整性错误"""
    pass


# ====== 错误严重程度 ======

class ErrorSeverity(Enum):
    """错误严重程度枚举"""
    LOW = "low"           # 警告级别，不影响主要功能
    MEDIUM = "medium"     # 影响部分功能，需要注意
    HIGH = "high"         # 严重影响，需要立即处理
    CRITICAL = "critical" # 致命错误，可能导致系统崩溃


# ====== 错误上下文 ======

@dataclass
class ErrorContext:
    """错误上下文信息"""
    module: str                    # 发生错误的模块
    function: str                  # 发生错误的函数
    satellite_id: Optional[str] = None    # 相关卫星ID
    time_step: Optional[int] = None       # 时间步
    additional_info: Dict[str, Any] = None  # 额外信息
    
    def __post_init__(self):
        if self.additional_info is None:
            self.additional_info = {}


# ====== 错误处理策略 ======

class ErrorHandlingStrategy:
    """错误处理策略基类"""
    
    def handle_error(self, error: SpaceSimulationError, context: ErrorContext) -> bool:
        """
        处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文
            
        Returns:
            bool: 是否成功处理错误
        """
        raise NotImplementedError
    
    def can_handle(self, error: SpaceSimulationError) -> bool:
        """检查是否可以处理该错误"""
        return True


class LogOnlyStrategy(ErrorHandlingStrategy):
    """仅记录日志的策略"""
    
    def handle_error(self, error: SpaceSimulationError, context: ErrorContext) -> bool:
        severity_map = {
            ErrorSeverity.LOW: logging.WARNING,
            ErrorSeverity.MEDIUM: logging.ERROR,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }
        
        severity = getattr(error, 'severity', ErrorSeverity.MEDIUM)
        log_level = severity_map.get(severity, logging.ERROR)
        
        logger.log(log_level, 
                  f"错误处理 - 模块: {context.module}, 函数: {context.function}, "
                  f"错误: {error.message}, 代码: {error.error_code}")
        
        return True


class RetryStrategy(ErrorHandlingStrategy):
    """重试策略"""
    
    def __init__(self, max_retries: int = 3, delay: float = 1.0):
        self.max_retries = max_retries
        self.delay = delay
        self._retry_counts: Dict[str, int] = {}
    
    def handle_error(self, error: SpaceSimulationError, context: ErrorContext) -> bool:
        error_key = f"{context.module}_{context.function}_{error.error_code}"
        
        if error_key not in self._retry_counts:
            self._retry_counts[error_key] = 0
        
        if self._retry_counts[error_key] < self.max_retries:
            self._retry_counts[error_key] += 1
            logger.warning(f"重试错误处理 ({self._retry_counts[error_key]}/{self.max_retries}): {error.message}")
            time.sleep(self.delay)
            return True
        else:
            logger.error(f"重试次数耗尽，错误无法恢复: {error.message}")
            # 重置计数器
            self._retry_counts[error_key] = 0
            return False
    
    def can_handle(self, error: SpaceSimulationError) -> bool:
        # 只对特定类型的错误进行重试
        return isinstance(error, (CommunicationError, ResourceError))


class GracefulDegradationStrategy(ErrorHandlingStrategy):
    """优雅降级策略"""
    
    def handle_error(self, error: SpaceSimulationError, context: ErrorContext) -> bool:
        logger.info(f"执行优雅降级处理: {error.message}")
        
        # 根据错误类型采取不同的降级措施
        if isinstance(error, CommunicationError):
            return self._handle_communication_degradation(error, context)
        elif isinstance(error, ResourceError):
            return self._handle_resource_degradation(error, context)
        elif isinstance(error, TaskExecutionError):
            return self._handle_task_degradation(error, context)
        
        return False
    
    def _handle_communication_degradation(self, error: CommunicationError, context: ErrorContext) -> bool:
        """处理通信错误的降级"""
        logger.info("降级到本地处理模式")
        # 可以在这里实现具体的降级逻辑
        return True
    
    def _handle_resource_degradation(self, error: ResourceError, context: ErrorContext) -> bool:
        """处理资源错误的降级"""
        logger.info("降低资源使用需求")
        # 可以在这里实现资源释放或优化逻辑
        return True
    
    def _handle_task_degradation(self, error: TaskExecutionError, context: ErrorContext) -> bool:
        """处理任务执行错误的降级"""
        logger.info("调整任务执行策略")
        # 可以在这里实现任务重新调度逻辑
        return True


# ====== 错误处理器 ======

class ErrorHandler:
    """
    统一错误处理器
    
    职责：
    1. 管理错误处理策略
    2. 分发错误到适当的策略
    3. 记录错误统计信息
    4. 提供错误监控接口
    """
    
    def __init__(self):
        self.strategies: list[ErrorHandlingStrategy] = []
        self.error_stats: Dict[str, int] = {}
        self.error_history: list[Dict[str, Any]] = []
        
        # 默认策略
        self.add_strategy(LogOnlyStrategy())
        self.add_strategy(RetryStrategy())
        self.add_strategy(GracefulDegradationStrategy())
        
        logger.info("错误处理器初始化完成")
    
    def add_strategy(self, strategy: ErrorHandlingStrategy):
        """添加错误处理策略"""
        self.strategies.append(strategy)
        logger.debug(f"添加错误处理策略: {strategy.__class__.__name__}")
    
    def handle_error(self, error: SpaceSimulationError, context: ErrorContext) -> bool:
        """
        处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文
            
        Returns:
            bool: 是否成功处理错误
        """
        # 记录错误统计
        self._record_error_stats(error, context)
        
        # 记录错误历史
        self._record_error_history(error, context)
        
        # 按策略处理错误
        for strategy in self.strategies:
            if strategy.can_handle(error):
                try:
                    if strategy.handle_error(error, context):
                        logger.debug(f"错误已被策略 {strategy.__class__.__name__} 成功处理")
                        return True
                except Exception as e:
                    logger.error(f"错误处理策略执行失败: {e}")
                    continue
        
        logger.error("所有错误处理策略都无法处理该错误")
        return False
    
    def _record_error_stats(self, error: SpaceSimulationError, context: ErrorContext):
        """记录错误统计信息"""
        error_type = error.__class__.__name__
        if error_type not in self.error_stats:
            self.error_stats[error_type] = 0
        self.error_stats[error_type] += 1
    
    def _record_error_history(self, error: SpaceSimulationError, context: ErrorContext):
        """记录错误历史"""
        error_record = {
            'timestamp': time.time(),
            'error_type': error.__class__.__name__,
            'message': error.message,
            'error_code': error.error_code,
            'module': context.module,
            'function': context.function,
            'satellite_id': context.satellite_id,
            'time_step': context.time_step
        }
        
        self.error_history.append(error_record)
        
        # 限制历史记录长度
        if len(self.error_history) > 1000:
            self.error_history = self.error_history[-500:]
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        total_errors = sum(self.error_stats.values())
        return {
            'total_errors': total_errors,
            'error_by_type': self.error_stats.copy(),
            'recent_errors': len([e for e in self.error_history if time.time() - e['timestamp'] < 3600])
        }
    
    def get_recent_errors(self, limit: int = 10) -> list[Dict[str, Any]]:
        """获取最近的错误记录"""
        return sorted(self.error_history, key=lambda x: x['timestamp'], reverse=True)[:limit]


# ====== 全局错误处理器实例 ======
_global_error_handler = ErrorHandler()


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器实例"""
    return _global_error_handler


# ====== 装饰器 ======

def handle_errors(module: str = "unknown", function: str = "unknown"):
    """
    错误处理装饰器
    
    Args:
        module: 模块名称
        function: 函数名称
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except SpaceSimulationError as e:
                context = ErrorContext(
                    module=module,
                    function=function or func.__name__
                )
                
                # 尝试从参数中提取额外的上下文信息
                if args and hasattr(args[0], 'satellite_id'):
                    context.satellite_id = getattr(args[0], 'satellite_id')
                
                # 处理错误
                if _global_error_handler.handle_error(e, context):
                    # 根据情况返回默认值或重新抛出
                    logger.info("错误已处理，继续执行")
                    return None
                else:
                    logger.error("错误处理失败，重新抛出异常")
                    raise
            except Exception as e:
                # 将非仿真错误转换为仿真错误
                simulation_error = SpaceSimulationError(
                    message=str(e),
                    error_code="UNEXPECTED_ERROR",
                    context={'original_exception': e.__class__.__name__}
                )
                
                context = ErrorContext(
                    module=module,
                    function=function or func.__name__
                )
                
                _global_error_handler.handle_error(simulation_error, context)
                raise simulation_error from e
                
        return wrapper
    return decorator


# ====== 便捷函数 ======

def raise_error(error_type: type, message: str, error_code: str = None, **context_kwargs):
    """
    便捷的错误抛出函数
    
    Args:
        error_type: 错误类型
        message: 错误消息
        error_code: 错误代码
        **context_kwargs: 上下文信息
    """
    error = error_type(
        message=message,
        error_code=error_code,
        context=context_kwargs
    )
    raise error


def safe_execute(func: Callable, *args, default_return=None, **kwargs):
    """
    安全执行函数，捕获并处理所有异常
    
    Args:
        func: 要执行的函数
        *args: 位置参数
        default_return: 发生异常时的默认返回值
        **kwargs: 关键字参数
        
    Returns:
        函数执行结果或默认返回值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"安全执行失败: {e}")
        return default_return


if __name__ == "__main__":
    # 简单测试
    @handle_errors(module="test", function="test_function")
    def test_function():
        raise CommunicationError("测试通信错误", "TEST_ERROR")
    
    try:
        test_function()
    except Exception as e:
        print(f"捕获异常: {e}")
    
    # 查看错误统计
    stats = get_error_handler().get_error_stats()
    print(f"错误统计: {stats}")