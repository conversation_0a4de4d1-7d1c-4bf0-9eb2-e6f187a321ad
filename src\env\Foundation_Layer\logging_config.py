"""
统一日志和配置管理系统
替换项目中散布的print语句，提供标准化的日志记录和配置管理
"""
import logging
import logging.handlers
import yaml
import os
import json
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import sys


class LoggingManager:
    """
    日志管理器 - 统一管理所有模块的日志记录
    特性：
    1. 多级别日志输出
    2. 文件轮转
    3. 格式化输出
    4. 性能友好
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.loggers = {}
        self._setup_root_logger()
        
    def _setup_root_logger(self):
        """设置根日志记录器"""
        # 获取配置
        log_config = self.config.get('logging', {})
        log_level = log_config.get('level', 'INFO')
        log_dir = log_config.get('directory', 'logs')
        max_file_size = log_config.get('max_file_size_mb', 10) * 1024 * 1024
        backup_count = log_config.get('backup_count', 5)
        
        # 创建日志目录
        Path(log_dir).mkdir(parents=True, exist_ok=True)
        
        # 设置根日志级别
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper()))
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建格式化器
        formatter = logging.Formatter(
            fmt='%(asctime)s [%(levelname)8s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        if log_config.get('console_output', True):
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            console_handler.setLevel(getattr(logging, log_level.upper()))
            root_logger.addHandler(console_handler)
        
        # 文件处理器（轮转）
        if log_config.get('file_output', True):
            log_file = os.path.join(log_dir, 'space_simulation.log')
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.DEBUG)  # 文件记录所有级别
            root_logger.addHandler(file_handler)
        
        # 错误文件处理器
        error_file = os.path.join(log_dir, 'errors.log')
        error_handler = logging.handlers.RotatingFileHandler(
            error_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setFormatter(formatter)
        error_handler.setLevel(logging.ERROR)
        root_logger.addHandler(error_handler)
        
        logging.info("日志系统初始化完成")
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志记录器"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def set_module_log_level(self, module_name: str, level: str):
        """设置特定模块的日志级别"""
        logger = self.get_logger(module_name)
        logger.setLevel(getattr(logging, level.upper()))
        logging.info(f"模块 {module_name} 日志级别设置为 {level}")
    
    def create_performance_logger(self) -> logging.Logger:
        """创建专用的性能日志记录器"""
        perf_logger = logging.getLogger('performance')
        
        # 性能日志文件
        log_dir = self.config.get('logging', {}).get('directory', 'logs')
        perf_file = os.path.join(log_dir, 'performance.log')
        
        perf_handler = logging.handlers.RotatingFileHandler(
            perf_file,
            maxBytes=5 * 1024 * 1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        
        perf_formatter = logging.Formatter(
            '%(asctime)s [PERF] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S.%f'
        )
        perf_handler.setFormatter(perf_formatter)
        perf_logger.addHandler(perf_handler)
        perf_logger.setLevel(logging.INFO)
        
        return perf_logger
    
    def cleanup_old_logs(self, days: int = 7):
        """清理旧的日志文件"""
        log_dir = Path(self.config.get('logging', {}).get('directory', 'logs'))
        if not log_dir.exists():
            return
        
        cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
        cleaned_files = 0
        
        for log_file in log_dir.glob('*.log*'):
            if log_file.stat().st_mtime < cutoff_time:
                try:
                    log_file.unlink()
                    cleaned_files += 1
                except Exception as e:
                    logging.warning(f"清理日志文件失败 {log_file}: {e}")
        
        if cleaned_files > 0:
            logging.info(f"清理了 {cleaned_files} 个旧日志文件")


class ConfigurationManager:
    """
    配置管理器 - 统一管理系统配置
    特性：
    1. 多层级配置合并
    2. 环境变量支持
    3. 配置验证
    4. 动态配置更新
    """
    
    def __init__(self, config_file: str = "src/env/config.yaml"):
        self.config_file = config_file
        self.config = {}
        self.defaults = {}
        self.environment_overrides = {}
        self._load_configuration()
    
    def _load_configuration(self):
        """加载配置文件"""
        try:
            # 加载主配置文件
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}
            else:
                logging.warning(f"配置文件 {self.config_file} 不存在，使用默认配置")
                self.config = {}
            
            # 加载默认配置
            self._load_default_configuration()
            
            # 应用环境变量覆盖
            self._apply_environment_overrides()
            
            # 合并配置
            self._merge_configurations()
            
            logging.info(f"配置加载完成: {self.config_file}")
            
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            self.config = self._get_minimal_config()
    
    def _load_default_configuration(self):
        """加载默认配置"""
        self.defaults = {
            'system': {
                'timeslot_duration_s': 10,
                'total_timeslots': 1000,
                'num_leo_satellites': 36,
                'num_users': 420,
                'earth_radius_m': 6371000,
                'leo_altitude_m': 550000,
                'visibility_threshold_m': 2000000,
                'visibility_earth_m': 2000000,
                'simulation_start_time': '2025-06-08 04:00:00'
            },
            'logging': {
                'level': 'INFO',
                'directory': 'logs',
                'console_output': True,
                'file_output': True,
                'max_file_size_mb': 10,
                'backup_count': 5
            },
            'communication': {
                'frequency_ghz': 28.0,
                'transmit_power_w': 1.0,
                'antenna_gain_db': 20.0,
                'bandwidth_mhz': 100.0,
                'noise_temperature_k': 300.0,
                'cache_duration_steps': 5
            },
            'energy': {
                'battery_capacity_j': 3600,
                'solar_power_w': 200,
                'base_power_w': 50
            },
            'computation': {
                'cpu_cores': 4,
                'cpu_frequency_ghz': 2.5,
                'memory_gb': 8.0,
                'storage_gb': 64.0
            },
            'reward': {
                'task_completion_reward': 10.0,
                'energy_efficiency_weight': 0.1,
                'resource_utilization_weight': 5.0
            },
            'performance': {
                'cache_max_memory_mb': 512,
                'enable_performance_monitoring': True,
                'gc_threshold_mb': 1000
            }
        }
    
    def _apply_environment_overrides(self):
        """应用环境变量覆盖"""
        env_prefix = "SPACE_SIM_"
        
        for key, value in os.environ.items():
            if key.startswith(env_prefix):
                config_key = key[len(env_prefix):].lower().replace('_', '.')
                self.environment_overrides[config_key] = self._convert_env_value(value)
    
    def _convert_env_value(self, value: str) -> Any:
        """转换环境变量值为合适的类型"""
        # 尝试转换为数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # 尝试转换为布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 返回字符串
        return value
    
    def _merge_configurations(self):
        """合并配置（默认配置 + 文件配置 + 环境变量）"""
        merged_config = self.defaults.copy()
        
        # 深度合并文件配置
        self._deep_merge(merged_config, self.config)
        
        # 应用环境变量覆盖
        for key, value in self.environment_overrides.items():
            keys = key.split('.')
            self._set_nested_value(merged_config, keys, value)
        
        self.config = merged_config
    
    def _deep_merge(self, base: Dict, override: Dict):
        """深度合并字典"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def _set_nested_value(self, config: Dict, keys: list, value: Any):
        """设置嵌套字典值"""
        current = config
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        current[keys[-1]] = value
    
    def _get_minimal_config(self) -> Dict[str, Any]:
        """获取最小可用配置"""
        return {
            'system': {
                'timeslot_duration_s': 10,
                'total_timeslots': 1000
            },
            'logging': {
                'level': 'INFO'
            }
        }
    
    def get_config(self, key_path: str = None, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置路径，如 'system.timeslot_duration_s'
            default: 默认值
            
        Returns:
            配置值
        """
        if key_path is None:
            return self.config
        
        keys = key_path.split('.')
        current = self.config
        
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return default
    
    def set_config(self, key_path: str, value: Any):
        """
        设置配置值
        
        Args:
            key_path: 配置路径
            value: 配置值
        """
        keys = key_path.split('.')
        self._set_nested_value(self.config, keys, value)
        logging.info(f"配置更新: {key_path} = {value}")
    
    def save_config(self, file_path: str = None):
        """保存配置到文件"""
        if file_path is None:
            file_path = self.config_file
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            logging.info(f"配置已保存到: {file_path}")
        except Exception as e:
            logging.error(f"保存配置失败: {e}")
    
    def validate_config(self) -> list[str]:
        """验证配置并返回错误列表"""
        errors = []
        
        # 验证必要的配置项
        required_configs = [
            ('system.timeslot_duration_s', (int, float)),
            ('system.total_timeslots', int),
            ('system.num_leo_satellites', int),
            ('logging.level', str)
        ]
        
        for config_path, expected_type in required_configs:
            value = self.get_config(config_path)
            if value is None:
                errors.append(f"缺少必需的配置项: {config_path}")
            elif not isinstance(value, expected_type):
                errors.append(f"配置项类型错误 {config_path}: 期望 {expected_type}, 实际 {type(value)}")
        
        # 验证数值范围
        if self.get_config('system.timeslot_duration_s', 0) <= 0:
            errors.append("system.timeslot_duration_s 必须大于0")
        
        if self.get_config('system.total_timeslots', 0) <= 0:
            errors.append("system.total_timeslots 必须大于0")
        
        return errors


# 全局实例
_logging_manager = None
_config_manager = None


def initialize_logging_and_config(config_file: str = "src/env/config.yaml"):
    """初始化日志和配置管理"""
    global _logging_manager, _config_manager
    
    try:
        # 初始化配置管理器
        _config_manager = ConfigurationManager(config_file)
        
        # 验证配置
        config_errors = _config_manager.validate_config()
        if config_errors:
            print(f"配置验证错误: {config_errors}")
        
        # 初始化日志管理器
        _logging_manager = LoggingManager(_config_manager.get_config())
        
        logging.info("日志和配置系统初始化完成")
        
    except Exception as e:
        print(f"日志和配置系统初始化失败: {e}")
        # 使用最小配置
        _config_manager = ConfigurationManager()
        _logging_manager = LoggingManager({})


def get_logger(name: str = None) -> logging.Logger:
    """获取日志记录器"""
    if _logging_manager is None:
        initialize_logging_and_config()
    
    if name is None:
        return logging.getLogger()
    else:
        return _logging_manager.get_logger(name)


def get_config_manager() -> ConfigurationManager:
    """获取配置管理器"""
    if _config_manager is None:
        initialize_logging_and_config()
    return _config_manager


def get_config(key_path: str = None, default: Any = None) -> Any:
    """便捷的配置获取函数"""
    return get_config_manager().get_config(key_path, default)


# 替换print语句的便捷函数
def log_info(message: str, module: str = "system"):
    """记录信息日志"""
    get_logger(module).info(message)


def log_warning(message: str, module: str = "system"):
    """记录警告日志"""
    get_logger(module).warning(message)


def log_error(message: str, module: str = "system"):
    """记录错误日志"""
    get_logger(module).error(message)


def log_debug(message: str, module: str = "system"):
    """记录调试日志"""
    get_logger(module).debug(message)


if __name__ == "__main__":
    # 测试日志和配置系统
    print("日志和配置系统测试开始...")
    
    # 初始化系统
    initialize_logging_and_config()
    
    # 测试日志记录
    log_info("这是一条信息日志")
    log_warning("这是一条警告日志") 
    log_error("这是一条错误日志")
    log_debug("这是一条调试日志")
    
    # 测试配置获取
    config = get_config()
    print(f"完整配置: {config}")
    
    timeslot_duration = get_config('system.timeslot_duration_s', 10)
    print(f"时隙持续时间: {timeslot_duration}")
    
    # 测试配置设置
    config_mgr = get_config_manager()
    config_mgr.set_config('test.value', 42)
    test_value = get_config('test.value')
    print(f"测试配置值: {test_value}")
    
    print("日志和配置系统测试完成")