import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime
import logging

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../src'))

from env.physics_layer.communication_refactored import CommunicationManager
from env.physics_layer.orbital import OrbitalUpdater
from env.Foundation_Layer.time_manager import create_time_manager_from_config


def setup_logging():
    """配置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_communication.log'),
            logging.StreamHandler()
        ]
    )


def test_communication_calculations():
    """测试通信计算功能"""
    print("\n" + "="*80)
    print("测试通信链路计算功能")
    print("="*80)
    
    # 初始化通信管理器
    comm_manager = CommunicationManager()
    
    # 测试路径损耗计算
    print("\n1. 测试路径损耗计算")
    print("-"*40)
    test_distances = np.array([100, 500, 1000, 2000, 4000])  # km
    path_loss = comm_manager.calculate_path_loss(test_distances, comm_manager.rf_carrier_freq_hz)
    
    print(f"载波频率: {comm_manager.rf_carrier_freq_hz/1e9:.2f} GHz")
    print("\n距离(km) | 路径损耗(dB)")
    print("-"*25)
    for dist, loss in zip(test_distances, path_loss):
        print(f"{dist:8d} | {loss:12.2f}")
    
    # 测试接收功率计算
    print("\n2. 测试接收功率计算")
    print("-"*40)
    rx_power = comm_manager.calculate_received_power(
        tx_power_w=comm_manager.p_su_w,
        distance_km=test_distances,
        frequency_hz=comm_manager.rf_carrier_freq_hz
    )
    
    print(f"发射功率: {comm_manager.p_su_w} W ({comm_manager._w_to_dbm(comm_manager.p_su_w):.2f} dBm)")
    print(f"天线增益: {comm_manager.antenna_gain_db} dB")
    print(f"实现损耗: {comm_manager.implementation_loss_db} dB")
    print(f"雨衰余量: {comm_manager.rain_fade_margin_db} dB")
    
    print("\n距离(km) | 接收功率(dBm)")
    print("-"*25)
    for dist, power in zip(test_distances, rx_power):
        print(f"{dist:8d} | {power:13.2f}")
    
    # 测试SNR计算
    print("\n3. 测试信噪比计算")
    print("-"*40)
    snr = comm_manager.calculate_snr(rx_power, comm_manager.b_su_hz)
    
    print(f"带宽: {comm_manager.b_su_hz/1e6:.1f} MHz")
    print(f"噪声功率谱密度: {comm_manager.system_noise_dbm_hz} dBm/Hz")
    
    print("\n距离(km) | SNR(dB)")
    print("-"*25)
    for dist, s in zip(test_distances, snr):
        print(f"{dist:8d} | {s:8.2f}")
    
    # 测试数据速率计算
    print("\n4. 测试数据速率计算")
    print("-"*40)
    data_rate = comm_manager.calculate_data_rate(snr, comm_manager.b_su_hz)
    
    print(f"编码效率: {comm_manager.coding_efficiency}")
    
    print("\n距离(km) | 数据速率(Mbps)")
    print("-"*25)
    for dist, rate in zip(test_distances, data_rate):
        print(f"{dist:8d} | {rate/1e6:14.2f}")


def test_communication_matrices():
    """测试通信矩阵生成"""
    print("\n" + "="*80)
    print("测试通信矩阵生成")
    print("="*80)
    
    # 初始化通信管理器
    comm_manager = CommunicationManager()
    
    # 测试多个时间步
    test_timesteps = [0, 100, 500, 1000, 1440]
    
    results_summary = []
    
    for timestep in test_timesteps:
        print(f"\n时间步 {timestep}:")
        print("-"*40)
        
        try:
            # 获取卫星间通信矩阵
            isl_comm = comm_manager.get_isl_communication_matrix(timestep)
            
            # 获取卫星-地面通信矩阵
            sat_ground_comm = comm_manager.get_satellite_ground_communication_matrix(timestep)
            
            # 获取卫星-云通信矩阵
            sat_cloud_comm = comm_manager.get_satellite_cloud_communication_matrix(timestep)
            
            # 获取汇总指标
            metrics = comm_manager.get_link_quality_metrics(timestep)
            
            # 打印结果
            print("\n卫星间链路 (ISL):")
            print(f"  - 矩阵形状: {isl_comm['data_rate_bps'].shape}")
            print(f"  - 可见链路数: {metrics['isl']['total_links']}")
            print(f"  - 平均数据速率: {metrics['isl']['avg_data_rate_gbps']:.2f} Gbps")
            print(f"  - 平均延迟: {metrics['isl']['avg_delay_ms']:.2f} ms")
            
            print("\n卫星-地面链路:")
            print(f"  - 矩阵形状: {sat_ground_comm['uplink_data_rate_bps'].shape}")
            print(f"  - 可见链路数: {metrics['satellite_ground']['total_links']}")
            print(f"  - 平均上行速率: {metrics['satellite_ground']['avg_uplink_rate_mbps']:.2f} Mbps")
            print(f"  - 平均下行速率: {metrics['satellite_ground']['avg_downlink_rate_mbps']:.2f} Mbps")
            print(f"  - 平均延迟: {metrics['satellite_ground']['avg_delay_ms']:.2f} ms")
            
            print("\n卫星-云中心链路:")
            print(f"  - 矩阵形状: {sat_cloud_comm['uplink_data_rate_bps'].shape}")
            print(f"  - 可见链路数: {metrics['satellite_cloud']['total_links']}")
            print(f"  - 平均上行速率: {metrics['satellite_cloud']['avg_uplink_rate_mbps']:.2f} Mbps")
            print(f"  - 平均下行速率: {metrics['satellite_cloud']['avg_downlink_rate_mbps']:.2f} Mbps")
            print(f"  - 平均延迟: {metrics['satellite_cloud']['avg_delay_ms']:.2f} ms")
            
            # 保存结果用于汇总
            results_summary.append({
                'timestep': timestep,
                'isl_links': metrics['isl']['total_links'],
                'isl_avg_rate_gbps': metrics['isl']['avg_data_rate_gbps'],
                'sat_ground_links': metrics['satellite_ground']['total_links'],
                'sat_ground_uplink_mbps': metrics['satellite_ground']['avg_uplink_rate_mbps'],
                'sat_ground_downlink_mbps': metrics['satellite_ground']['avg_downlink_rate_mbps'],
                'sat_cloud_links': metrics['satellite_cloud']['total_links'],
                'sat_cloud_uplink_mbps': metrics['satellite_cloud']['avg_uplink_rate_mbps'],
                'sat_cloud_downlink_mbps': metrics['satellite_cloud']['avg_downlink_rate_mbps']
            })
            
        except Exception as e:
            print(f"  错误: {e}")
    
    # 打印汇总表格
    print("\n" + "="*80)
    print("通信链路汇总统计")
    print("="*80)
    
    df = pd.DataFrame(results_summary)
    print(df.to_string(index=False))
    
    # 保存到CSV
    df.to_csv('communication_test_results.csv', index=False)
    print("\n结果已保存到 communication_test_results.csv")


def test_specific_satellite_links():
    """测试特定卫星的链路状态"""
    print("\n" + "="*80)
    print("测试特定卫星链路状态")
    print("="*80)
    
    comm_manager = CommunicationManager()
    timestep = 100
    
    # 获取通信矩阵
    isl_comm = comm_manager.get_isl_communication_matrix(timestep)
    sat_ground_comm = comm_manager.get_satellite_ground_communication_matrix(timestep)
    
    # 选择第一颗卫星进行详细分析
    sat_idx = 0
    sat_id = isl_comm['satellite_ids'][sat_idx]
    
    print(f"\n卫星 {sat_id} 在时间步 {timestep} 的链路状态:")
    print("-"*60)
    
    # 分析ISL链路
    print("\n1. 星间链路 (ISL):")
    isl_visible = isl_comm['visibility'][sat_idx, :]
    isl_neighbors = np.where(isl_visible)[0]
    
    print(f"   可见卫星数: {len(isl_neighbors)}")
    if len(isl_neighbors) > 0:
        print(f"   可见卫星ID: {[isl_comm['satellite_ids'][i] for i in isl_neighbors[:5]]} ...")
        print(f"   距离范围: {isl_comm['distance_km'][sat_idx, isl_neighbors].min():.1f} - "
              f"{isl_comm['distance_km'][sat_idx, isl_neighbors].max():.1f} km")
        print(f"   数据速率: {isl_comm['data_rate_bps'][sat_idx, isl_neighbors[0]]/1e9:.1f} Gbps (激光链路)")
    
    # 分析地面链路
    print("\n2. 卫星-地面链路:")
    ground_visible = sat_ground_comm['visibility'][sat_idx, :]
    ground_stations = np.where(ground_visible)[0]
    
    print(f"   可见地面站数: {len(ground_stations)}")
    if len(ground_stations) > 0:
        sample_stations = ground_stations[:3]  # 取前3个地面站
        for gs_idx in sample_stations:
            gs_id = sat_ground_comm['ground_station_ids'][gs_idx]
            distance = sat_ground_comm['distance_km'][sat_idx, gs_idx]
            uplink = sat_ground_comm['uplink_data_rate_bps'][sat_idx, gs_idx] / 1e6
            downlink = sat_ground_comm['downlink_data_rate_bps'][sat_idx, gs_idx] / 1e6
            delay = sat_ground_comm['propagation_delay_ms'][sat_idx, gs_idx]
            
            print(f"\n   地面站 {gs_id}:")
            print(f"     - 距离: {distance:.1f} km")
            print(f"     - 上行速率: {uplink:.2f} Mbps")
            print(f"     - 下行速率: {downlink:.2f} Mbps")
            print(f"     - 传播延迟: {delay:.2f} ms")


def test_cache_performance():
    """测试缓存性能"""
    print("\n" + "="*80)
    print("测试缓存性能")
    print("="*80)
    
    import time
    
    comm_manager = CommunicationManager()
    timestep = 500
    
    # 第一次调用（无缓存）
    start_time = time.time()
    _ = comm_manager.get_isl_communication_matrix(timestep)
    first_call_time = time.time() - start_time
    
    # 第二次调用（有缓存）
    start_time = time.time()
    _ = comm_manager.get_isl_communication_matrix(timestep)
    second_call_time = time.time() - start_time
    
    print(f"\n第一次调用耗时: {first_call_time*1000:.2f} ms")
    print(f"第二次调用耗时: {second_call_time*1000:.2f} ms")
    if second_call_time > 0:
        print(f"加速比: {first_call_time/second_call_time:.2f}x")
    else:
        print(f"加速比: >1000x (第二次调用时间太短)")
    
    # 清除缓存
    comm_manager.clear_cache()
    
    # 再次调用（缓存已清除）
    start_time = time.time()
    _ = comm_manager.get_isl_communication_matrix(timestep)
    third_call_time = time.time() - start_time
    
    print(f"清除缓存后调用耗时: {third_call_time*1000:.2f} ms")


def main():
    """主测试函数"""
    setup_logging()
    
    print("="*80)
    print("通信链路管理模块测试")
    print("="*80)
    
    # 运行各项测试
    test_communication_calculations()
    test_communication_matrices()
    test_specific_satellite_links()
    test_cache_performance()
    
    print("\n" + "="*80)
    print("所有测试完成!")
    print("="*80)


if __name__ == "__main__":
    main()