"""
时间管理模块 - 统一处理仿真时间和物理时间的转换
提供标准化的时间操作接口，解决项目中时间处理不一致的问题
"""
from datetime import datetime, timedelta
from typing import Union, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class TimeContext:
    """时间上下文数据类，包含所有时间相关信息"""
    simulation_step: int           # 仿真步数 (0-based)
    simulation_time: float         # 仿真时间 (秒)
    physical_time: datetime        # 对应的物理时间
    timeslot_duration: float       # 时隙持续时间 (秒)
    
    def __post_init__(self):
        """验证时间上下文的一致性"""
        if self.simulation_step < 0:
            raise ValueError(f"仿真步数不能为负数: {self.simulation_step}")
        if self.simulation_time < 0:
            raise ValueError(f"仿真时间不能为负数: {self.simulation_time}")
        if self.timeslot_duration <= 0:
            raise ValueError(f"时隙持续时间必须为正数: {self.timeslot_duration}")


class TimeManager:
    """
    统一时间管理类
    
    职责：
    1. 统一仿真时间和物理时间的转换
    2. 提供标准化的时间查询接口
    3. 确保时间操作的一致性和准确性
    """
    
    def __init__(self, 
                 start_time: datetime,
                 timeslot_duration: float,
                 total_timeslots: int):
        """
        初始化时间管理器
        
        Args:
            start_time: 仿真开始的物理时间
            timeslot_duration: 每个时隙的持续时间 (秒)
            total_timeslots: 总时隙数
        """
        self.start_time = start_time
        self.timeslot_duration = timeslot_duration
        self.total_timeslots = total_timeslots
        
        logger.info(f"时间管理器初始化 - 开始时间: {start_time}, "
                   f"时隙时长: {timeslot_duration}s, 总时隙: {total_timeslots}")
    
    def get_time_context(self, simulation_step: int) -> TimeContext:
        """
        获取指定仿真步的完整时间上下文
        
        Args:
            simulation_step: 仿真步数 (0-based)
            
        Returns:
            TimeContext: 时间上下文对象
            
        Raises:
            ValueError: 当仿真步数超出范围时
        """
        if not self.is_valid_step(simulation_step):
            raise ValueError(f"仿真步数 {simulation_step} 超出有效范围 [0, {self.total_timeslots-1}]")
        
        simulation_time = simulation_step * self.timeslot_duration
        physical_time = self.start_time + timedelta(seconds=simulation_time)
        
        return TimeContext(
            simulation_step=simulation_step,
            simulation_time=simulation_time,
            physical_time=physical_time,
            timeslot_duration=self.timeslot_duration
        )
    
    def get_simulation_time(self, simulation_step: int) -> float:
        """获取仿真时间"""
        return self.get_time_context(simulation_step).simulation_time
    
    def get_physical_time(self, simulation_step: int) -> datetime:
        """获取物理时间"""
        return self.get_time_context(simulation_step).physical_time
    
    def get_simulation_step(self, physical_time: datetime) -> int:
        """
        根据物理时间计算对应的仿真步数
        
        Args:
            physical_time: 物理时间
            
        Returns:
            int: 仿真步数
        """
        time_diff = (physical_time - self.start_time).total_seconds()
        simulation_step = int(time_diff / self.timeslot_duration)
        
        if not self.is_valid_step(simulation_step):
            logger.warning(f"计算得到的仿真步数 {simulation_step} 超出有效范围")
            simulation_step = max(0, min(simulation_step, self.total_timeslots - 1))
        
        return simulation_step
    
    def is_valid_step(self, simulation_step: int) -> bool:
        """检查仿真步数是否有效"""
        return 0 <= simulation_step < self.total_timeslots
    
    def get_time_range(self, start_step: int, end_step: int) -> list[TimeContext]:
        """
        获取指定范围内的时间上下文列表
        
        Args:
            start_step: 起始步数 (包含)
            end_step: 结束步数 (不包含)
            
        Returns:
            list[TimeContext]: 时间上下文列表
        """
        if start_step < 0 or end_step > self.total_timeslots or start_step >= end_step:
            raise ValueError(f"无效的时间范围: [{start_step}, {end_step})")
        
        return [self.get_time_context(step) for step in range(start_step, end_step)]
    
    def calculate_duration_between_steps(self, start_step: int, end_step: int) -> float:
        """
        计算两个仿真步之间的时间间隔
        
        Args:
            start_step: 起始步数
            end_step: 结束步数
            
        Returns:
            float: 时间间隔 (秒)
        """
        if start_step > end_step:
            raise ValueError("起始步数不能大于结束步数")
        
        return (end_step - start_step) * self.timeslot_duration
    
    def add_time_delta(self, simulation_step: int, delta_seconds: float) -> Optional[int]:
        """
        在指定时间步基础上增加时间偏移，返回新的时间步
        
        Args:
            simulation_step: 当前时间步
            delta_seconds: 时间偏移 (秒)
            
        Returns:
            Optional[int]: 新的时间步，如果超出范围则返回None
        """
        current_time = self.get_simulation_time(simulation_step)
        new_time = current_time + delta_seconds
        new_step = int(new_time / self.timeslot_duration)
        
        return new_step if self.is_valid_step(new_step) else None
    
    def get_progress_info(self, current_step: int) -> dict:
        """
        获取仿真进度信息
        
        Args:
            current_step: 当前时间步
            
        Returns:
            dict: 包含进度信息的字典
        """
        progress = (current_step + 1) / self.total_timeslots
        remaining_steps = self.total_timeslots - current_step - 1
        remaining_time = remaining_steps * self.timeslot_duration
        
        return {
            'current_step': current_step,
            'total_steps': self.total_timeslots,
            'progress_percent': progress * 100,
            'remaining_steps': remaining_steps,
            'remaining_time_seconds': remaining_time,
            'elapsed_time_seconds': current_step * self.timeslot_duration
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"TimeManager(start={self.start_time}, "
                f"duration={self.timeslot_duration}s, "
                f"slots={self.total_timeslots})")
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()


def create_time_manager_from_config(config: dict) -> TimeManager:
    """
    从配置文件创建时间管理器
    
    Args:
        config: 配置字典，应包含时间相关配置
        
    Returns:
        TimeManager: 时间管理器实例
    """
    try:
        # 从配置中提取时间参数
        system_config = config.get('system', {})
        
        # 默认开始时间 (可以从配置文件中读取)
        start_time_str = system_config.get('simulation_start_time', '2025-06-08 04:00:00')
        start_time = datetime.strptime(start_time_str, '%Y-%m-%d %H:%M:%S')
        
        timeslot_duration = system_config.get('timeslot_duration_s', 10)
        total_timeslots = system_config.get('total_timeslots', 1000)
        
        return TimeManager(
            start_time=start_time,
            timeslot_duration=timeslot_duration,
            total_timeslots=total_timeslots
        )
        
    except Exception as e:
        logger.error(f"从配置创建时间管理器失败: {e}")
        raise ValueError(f"无效的时间配置: {e}")


# 模块级别的便捷函数
def validate_time_consistency(*time_contexts: TimeContext) -> bool:
    """
    验证多个时间上下文的一致性
    
    Args:
        *time_contexts: 要验证的时间上下文
        
    Returns:
        bool: 是否一致
    """
    if len(time_contexts) <= 1:
        return True
    
    reference = time_contexts[0]
    for context in time_contexts[1:]:
        if context.timeslot_duration != reference.timeslot_duration:
            logger.warning("时隙持续时间不一致")
            return False
    
    return True


if __name__ == "__main__":
    # 简单测试
    start_time = datetime(2025, 6, 8, 4, 0, 0)
    tm = TimeManager(start_time, 10.0, 1000)
    
    # 测试基本功能
    context = tm.get_time_context(100)
    print(f"时间上下文: {context}")
    
    # 测试进度信息
    progress = tm.get_progress_info(500)
    print(f"仿真进度: {progress}")